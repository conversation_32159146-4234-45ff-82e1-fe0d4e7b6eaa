"use client";
import type { GameList } from "@/app/api/game/list/query";
import { AppButton } from "@/app/components/buttons/app-button";
import { GameButton } from "@/app/components/buttons/game-button";
import { FullScreenImage } from "@/app/components/full-screen";
import { TexturedText } from "@/app/components/textured-text";
import { GameId } from "@/app/constants";
import { imageUrl } from "@/utils/image-url";
import { useMutation, useQuery } from "@tanstack/react-query";
import clsx from "clsx";
import Image from "next/image";
import { useCallback, useState } from "react";
import { BalanceGameApp } from "./game-app/balance-game-app";
import { GameEntries, GameState } from "./game-entries";
import { GameResult } from "./game-result";
import style from "./index.module.scss";

enum Stage {
  Nickname = "nickname",
  Entrance = "entrance",
  Instruction = "Instruction",
  Play = "play",
  Result = "Result",
}

const GamePage = () => {
  const [nickname, setNickname] = useState<string>("");

  const [stage, setStage] = useState<Stage>(Stage.Nickname);
  const [gameId, setGameId] = useState<GameId>("balance");
  const [score, setScore] = useState(0);
  const [gameRecordId, setGameRecordId] = useState<string>("");

  const { data: gameList } = useQuery({
    queryFn: async (): Promise<GameState> => {
      const res: GameList = await fetch("/api/game/list").then((r) => r.json());

      const state: GameState = {};

      res?.forEach((item) => {
        const { name, startTime, GameRecord } = item;
        state[name] = {
          available: new Date(startTime).getTime() <= Date.now(),
          played: !!GameRecord.length,
        } satisfies GameState[number];
      });

      return state;
    },
    queryKey: ["game", "list"],
  });

  const { mutate: createRecord } = useMutation({
    mutationFn: async (score: number) => {
      console.log("mutate");
      const response = await fetch("/api/record/new", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ score, nickname, gameId }),
      });

      if (response.ok) {
        const result = await response.json();
        return result;
      } else {
        throw new Error("Failed to create record");
      }
    },
    onSuccess: (data) => {
      if (data?.id) {
        setGameRecordId(data.id);
      }
    },
    onError: (error) => {
      console.error("Error creating record:", error);
    },
  });

  const onGameEnd = useCallback(
    (score: number) => {
      createRecord(score);
      setScore(score);
      setTimeout(() => setStage(Stage.Result), 1000);
    },
    [createRecord],
  );

  if (stage === Stage.Nickname) {
    return (
      <>
        <FullScreenImage src={imageUrl("/screen-login.png")} />
        <form
          className="flex flex-col items-center relative pt-[56.5vw]"
          onSubmit={(e) => {
            e.preventDefault();
            if (nickname) {
              setStage(Stage.Entrance);
            } else {
              alert("請輸入暱稱");
            }
          }}
        >
          <TexturedText className="font-[1000] text-[5.5vw]">
            請輸入您的暱稱
          </TexturedText>

          <input
            name="nickname"
            className={clsx(style.input)}
            onChange={(e) => {
              setNickname(e.target.value);
            }}
          />

          <AppButton disabled={!nickname}>開始遊戲</AppButton>
        </form>
      </>
    );
  }

  if (stage === Stage.Entrance) {
    return (
      <>
        <FullScreenImage src={imageUrl("/screen-game-entries.png")} />
        <div className="relative z-0 flex flex-col items-center pt-[38vw]">
          <GameEntries
            onEntryClick={(gameId) => {
              setGameId(gameId);
              setStage(Stage.Instruction);
            }}
            gameState={gameList}
          />
        </div>
      </>
    );
  }

  const renderTitle = () => {
    const image = {
      balance: "/game-title-balance.png",
      catch: "/game-title-catch.png",
      quiz: "/game-title-quiz.png",
    }[gameId];

    const offset = {
      balance: "pt-[5vw]",
      catch: "pt-[2vw]",
      quiz: "pb-[1vw]",
    }[gameId];

    return (
      <Image
        unoptimized
        alt=""
        className={clsx("w-full h-auto", offset)}
        src={imageUrl(image)}
        width={786}
        height={258}
      />
    );
  };

  const renderInstruction = () => {
    switch (gameId) {
      case "balance":
        return (
          <>
            限時1分鐘
            <br />
            左右移動控制威金森碳酸水
            <br />
            讓上方滾動的瓶蓋維持平衡
            <br />
            堅持越久「超威積分」越高
          </>
        );
      case "catch":
        return (
          <>
            限時1分鐘
            <br />
            左右移動控制威金森碳酸水
            <br />
            讓上方滾動的瓶蓋維持平衡
            <br />
            堅持越久「超威積分」越高
          </>
        );
      case "quiz":
        return (
          <>
            資深威粉來喊聲
            <br />
            挑戰經典威金森問答
            <span className="inline-block mx-[-0.25em]">！</span>
            <br />
            每關限時10秒，共5小關
            <br />
            答對一題得20分
            <br />
            答錯則倒扣10分
          </>
        );
      default:
        return null;
    }
  };

  if (stage === Stage.Instruction) {
    return (
      <>
        <FullScreenImage src={imageUrl("/screen-game.png")} />
        <div className="relative z-0 flex flex-col items-center pt-[4vw]">
          <div className="w-[73vw] h-[23.5vw] flex justify-center items-center">
            {renderTitle()}
          </div>

          <div
            className="w-[77vw] h-[113vw] flex flex-col items-center pt-[13vw]"
            style={{
              backgroundImage: `url(${imageUrl("/game-instruction-dialog.png")})`,
              backgroundSize: "contain",
              backgroundRepeat: "no-repeat",
              backgroundPosition: "center",
            }}
          >
            <h2 className="text-[8.7vw] font-[1000] text-[#fff100]">
              遊戲說明
            </h2>
            <p className="h-[57vw] text-center text-[4vw] leading-[2.1] font-bold">
              {renderInstruction()}
            </p>
            <GameButton onClick={() => setStage(Stage.Play)}>START</GameButton>
          </div>
        </div>
      </>
    );
  }

  if (stage === Stage.Play) {
    return (
      <>
        <FullScreenImage src={imageUrl("/screen-game.png")} />
        <div className="relative z-0 flex flex-col items-center pt-[4vw]">
          <div className="w-[73vw] h-[23.5vw] flex justify-center items-center">
            {renderTitle()}
          </div>
          <div>
            <BalanceGameApp onGameEnd={onGameEnd} />
          </div>
        </div>
      </>
    );
  }

  if (stage === Stage.Result) {
    return (
      <GameResult
        gameTitle={renderTitle()}
        nickname={nickname}
        gameId={gameId}
        score={score}
        gameRecordId={gameRecordId}
        onPlayAgain={() => {
          setGameRecordId("");
          setStage(Stage.Play);
        }}
      />
    );
  }
};

export default GamePage;
